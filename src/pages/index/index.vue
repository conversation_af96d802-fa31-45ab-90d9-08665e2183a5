<!-- 退伍兵倒计时应用首页 -->
<route lang="jsonc" type="home">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "退伍倒计时"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { tabbarStore } from '@/tabbar/store'

defineOptions({
  name: 'VeteranCountdown',
})

// 获取屏幕边界到安全区域距离
let safeAreaInsets
let systemInfo

// #ifdef MP-WEIXIN
systemInfo = uni.getWindowInfo()
safeAreaInsets = systemInfo.safeArea
  ? {
      top: systemInfo.safeArea.top,
      right: systemInfo.windowWidth - systemInfo.safeArea.right,
      bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
      left: systemInfo.safeArea.left,
    }
  : null
// #endif

// #ifndef MP-WEIXIN
systemInfo = uni.getSystemInfoSync()
safeAreaInsets = systemInfo.safeAreaInsets
// #endif

// 退伍日期相关
const dischargeDate = ref('')
const currentTime = ref(new Date())
let timer: NodeJS.Timeout | null = null

// 背景样式列表
const backgroundStyles = [
  {
    type: 'gradient',
    style: 'linear-gradient(135deg, #2d5016 0%, #3e6b1f 25%, #4a7c23 50%, #3e6b1f 75%, #2d5016 100%)'
  },
  {
    type: 'gradient',
    style: 'linear-gradient(45deg, #1a4c2e 0%, #2d5016 30%, #4a7c23 60%, #2d5016 100%)'
  },
  {
    type: 'gradient',
    style: 'radial-gradient(circle at center, #4a7c23 0%, #3e6b1f 40%, #2d5016 80%)'
  }
]
const currentBgIndex = ref(0)

// 励志文案列表
const motivationalTexts = [
  {
    chinese: '青春有很多样子',
    english: 'There are many things about young'
  },
  {
    chinese: '很庆幸我的青春有穿军装的样子',
    english: "I'm glad that my young has the appearance of military uniform"
  },
  {
    chinese: '军营岁月，青春无悔',
    english: 'Military years, youth without regret'
  },
  {
    chinese: '铁血军魂，永远传承',
    english: 'Iron-blooded military soul, forever inherited'
  }
]
const currentTextIndex = ref(0)

// 计算倒计时
const countdown = computed(() => {
  if (!dischargeDate.value) return null

  const discharge = new Date(dischargeDate.value)
  const now = currentTime.value
  const diff = discharge.getTime() - now.getTime()

  if (diff <= 0) {
    return {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
      isExpired: true
    }
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((diff % (1000 * 60)) / 1000)

  return {
    days,
    hours,
    minutes,
    seconds,
    isExpired: false
  }
})

// 选择退伍日期
function selectDischargeDate() {
  uni.showModal({
    title: '选择退伍日期',
    content: '请选择您的退伍日期',
    showCancel: true,
    success: (res) => {
      if (res.confirm) {
        // 使用日期选择器
        const currentDate = new Date()
        const year = currentDate.getFullYear()
        const month = String(currentDate.getMonth() + 1).padStart(2, '0')
        const day = String(currentDate.getDate()).padStart(2, '0')

        uni.showActionSheet({
          itemList: ['30秒后(测试)', '1分钟后(测试)', '明天', '一周后', '一个月后', '自定义日期'],
          success: (res) => {
            const now = new Date()
            let selectedDate = new Date()

            switch (res.tapIndex) {
              case 0: // 30秒后(测试)
                selectedDate = new Date(now.getTime() + 30 * 1000)
                break
              case 1: // 1分钟后(测试)
                selectedDate = new Date(now.getTime() + 60 * 1000)
                break
              case 2: // 明天
                selectedDate = new Date(now.getTime() + 24 * 60 * 60 * 1000)
                break
              case 3: // 一周后
                selectedDate = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
                break
              case 4: // 一个月后
                selectedDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
                break
              case 5: // 自定义日期
                showDatePicker()
                return
            }

            // 对于测试选项，保存完整的日期时间
            if (res.tapIndex <= 1) {
              dischargeDate.value = selectedDate.toISOString()
            } else {
              dischargeDate.value = selectedDate.toISOString().split('T')[0]
            }
            uni.setStorageSync('dischargeDate', dischargeDate.value)
          }
        })
      }
    }
  })
}

// 显示日期选择器
function showDatePicker() {
  // 这里可以使用第三方日期选择器组件
  // 暂时使用简单的输入方式
  uni.showModal({
    title: '输入退伍日期',
    content: '请输入日期(格式: YYYY-MM-DD)',
    editable: true,
    placeholderText: '2024-12-31',
    success: (res) => {
      if (res.confirm && res.content) {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/
        if (dateRegex.test(res.content)) {
          dischargeDate.value = res.content
          uni.setStorageSync('dischargeDate', dischargeDate.value)
        } else {
          uni.showToast({
            title: '日期格式错误',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 切换背景
function switchBackground() {
  currentBgIndex.value = (currentBgIndex.value + 1) % backgroundStyles.length
}

// 切换文案
function switchText() {
  currentTextIndex.value = (currentTextIndex.value + 1) % motivationalTexts.length
}

// 格式化显示日期
function formatDisplayDate(dateStr: string) {
  if (!dateStr) return ''

  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  // 如果包含时间信息，显示完整日期时间
  if (dateStr.includes('T') || dateStr.includes(':')) {
    return `${year}-${month}-${day} ${hours}:${minutes}`
  }

  return `${year}-${month}-${day}`
}

// 生命周期
onMounted(() => {
  // 设置当前tabbar索引为首页
  tabbarStore.setAutoCurIdx('/pages/index/index')

  // 从本地存储加载退伍日期
  const savedDate = uni.getStorageSync('dischargeDate')
  if (savedDate) {
    dischargeDate.value = savedDate
  }

  // 启动定时器更新时间
  timer = setInterval(() => {
    currentTime.value = new Date()
  }, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<template root="uniKuRoot">
  <view class="veteran-countdown-container" :style="{ background: backgroundStyles[currentBgIndex].style }">
    <!-- 背景遮罩 -->
    <view class="background-mask"></view>

    <!-- 顶部设置按钮 -->
    <view class="top-controls" :style="{ paddingTop: `${safeAreaInsets?.top + 10}px` }">
      <view class="control-btn" @click="switchBackground">
        <text class="control-icon">🖼️</text>
      </view>
      <view class="control-btn" @click="switchText">
        <text class="control-icon">💬</text>
      </view>
    </view>

    <!-- 五星红旗 -->
    <view class="flag-container">
      <view class="china-flag">
        <view class="flag-red">
          <view class="star big-star"></view>
          <view class="star small-star star1"></view>
          <view class="star small-star star2"></view>
          <view class="star small-star star3"></view>
          <view class="star small-star star4"></view>
        </view>
      </view>
    </view>

    <!-- 励志文案 -->
    <view class="motivational-text">
      <view class="chinese-text">
        {{ motivationalTexts[currentTextIndex].chinese }}
      </view>
      <view class="english-text">
        {{ motivationalTexts[currentTextIndex].english }}
      </view>
    </view>

    <!-- 倒计时显示或选择日期按钮 -->
    <view class="countdown-section">
      <view v-if="!dischargeDate" class="select-date-container">
        <view class="select-date-btn" @click="selectDischargeDate">
          选择退伍日期
        </view>
      </view>

      <view v-else-if="countdown" class="countdown-display">
        <view v-if="countdown.isExpired" class="expired-message">
          <text class="expired-text">🎉 退伍快乐！🎉</text>
          <view class="reset-btn" @click="dischargeDate = ''">
            重新设置日期
          </view>
        </view>

        <view v-else class="countdown-grid">
          <view class="countdown-item">
            <view class="countdown-number">{{ countdown.days }}</view>
            <view class="countdown-label">天</view>
          </view>
          <view class="countdown-item">
            <view class="countdown-number">{{ countdown.hours }}</view>
            <view class="countdown-label">时</view>
          </view>
          <view class="countdown-item">
            <view class="countdown-number">{{ countdown.minutes }}</view>
            <view class="countdown-label">分</view>
          </view>
          <view class="countdown-item">
            <view class="countdown-number">{{ countdown.seconds }}</view>
            <view class="countdown-label">秒</view>
          </view>
        </view>

        <view class="discharge-date-info">
          <text>退伍日期：{{ formatDisplayDate(dischargeDate) }}</text>
          <view class="change-date-btn" @click="selectDischargeDate">
            修改日期
          </view>
        </view>
      </view>
    </view>

    <!-- 底部关注提示 -->
    <view class="bottom-notice">
      <view class="notice-content">
        <image src="/static/images/avatar.jpg" class="notice-avatar" />
        <text class="notice-text">关注兵哥哥小迷妹微信公众号</text>
        <view class="follow-btn">立即关注</view>
      </view>
    </view>



    <!-- 为tabbar预留空间，和关于页面一样 -->
    <view class="h-6" />
  </view>
</template>

<style lang="scss" scoped>
.veteran-countdown-container {
  position: relative;
  min-height: 100vh;
  padding: 20px;
  /* 添加纹理效果 */
  background-image:
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 2px,
      rgba(0, 0, 0, 0.02) 2px,
      rgba(0, 0, 0, 0.02) 4px
    );
}

.background-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 0, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 0, 0, 0.1) 0%, transparent 50%),
    linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.3) 0%,
      rgba(0, 0, 0, 0.1) 50%,
      rgba(0, 0, 0, 0.4) 100%
    );
  pointer-events: none;
  z-index: 1;
}

.top-controls {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-bottom: 20px;
}

.control-btn {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.control-icon {
  font-size: 18px;
}

.flag-container {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  margin: 40px 0;
}

.china-flag {
  width: 200px;
  height: 133px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: flagWave 3s ease-in-out infinite;
}

@keyframes flagWave {
  0%, 100% {
    transform: rotateY(0deg) rotateX(0deg);
  }
  25% {
    transform: rotateY(2deg) rotateX(1deg);
  }
  50% {
    transform: rotateY(0deg) rotateX(-1deg);
  }
  75% {
    transform: rotateY(-2deg) rotateX(1deg);
  }
}

.flag-red {
  width: 100%;
  height: 100%;
  background: #de2910;
  position: relative;
}

.star {
  position: absolute;
  background: #ffde00;
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
  animation: starTwinkle 2s ease-in-out infinite;
}

@keyframes starTwinkle {
  0%, 100% {
    opacity: 1;
    filter: brightness(1);
  }
  50% {
    opacity: 0.8;
    filter: brightness(1.2);
  }
}

.big-star {
  width: 30px;
  height: 30px;
  top: 20px;
  left: 30px;
}

.small-star {
  width: 12px;
  height: 12px;
}

.star1 {
  top: 10px;
  left: 80px;
  transform: rotate(15deg);
  animation-delay: 0.2s;
}

.star2 {
  top: 25px;
  left: 90px;
  transform: rotate(30deg);
  animation-delay: 0.4s;
}

.star3 {
  top: 45px;
  left: 85px;
  transform: rotate(-15deg);
  animation-delay: 0.6s;
}

.star4 {
  top: 35px;
  left: 70px;
  transform: rotate(-30deg);
  animation-delay: 0.8s;
}

.motivational-text {
  position: relative;
  z-index: 2;
  text-align: center;
  margin: 40px 0;
}

.chinese-text {
  font-size: 24px;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 10px;
}

.english-text {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  font-style: italic;
}

.countdown-section {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 40px 0;
}

.select-date-container {
  display: flex;
  justify-content: center;
}

.select-date-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 15px 40px;
  border-radius: 25px;
  font-size: 18px;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(238, 90, 36, 0.4);
  text-align: center;
}

.countdown-display {
  width: 100%;
  max-width: 400px;
}

.expired-message {
  text-align: center;
}

.expired-text {
  font-size: 28px;
  color: #ffd700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 20px;
}

.reset-btn, .change-date-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  text-align: center;
  margin-top: 10px;
  backdrop-filter: blur(10px);
}

.countdown-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin-bottom: 30px;
}

.countdown-item {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 15px;
  padding: 20px 10px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.countdown-number {
  font-size: 32px;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 5px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.countdown-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.discharge-date-info {
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.bottom-notice {
  position: relative;
  z-index: 2;
  margin: 40px 0 20px 0;
}

.notice-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.notice-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 10px;
}

.notice-text {
  flex: 1;
  color: white;
  font-size: 14px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.follow-btn {
  background: #1aad19;
  color: white;
  padding: 8px 16px;
  border-radius: 15px;
  font-size: 12px;
  text-align: center;
}


</style>
